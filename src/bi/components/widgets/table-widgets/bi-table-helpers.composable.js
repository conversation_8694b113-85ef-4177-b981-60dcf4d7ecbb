import { cloneDeep, get, orderBy } from 'lodash-es';
import { nextTick } from 'vue';
import { usePivotTableGenerator } from '../../../composables/helpers/bi-pivot-table-generator.composable.js';
import { usePivotDataProcessor } from '../../../composables/helpers/bi-pivot-data-processor.composable.js';
import { useTableConditionalFormatter } from '../../../composables/bi-table-conditional-formatter.composable.js';

/**
 * Comprehensive table manager composable
 * Handles all table-related functionality including setup, formatting, and interactions
 * Combines functionality from multiple specialized composables for better organization
 */
export function useTableManager() {
  // Import specialized composables
  const { generateNestedTableHeaders } = usePivotTableGenerator();
  const { generateHandsontableData } = usePivotDataProcessor();
  const { checkRuleMatch, getContrastTextColor, getLighterColor, getColorForValue } = useTableConditionalFormatter();

  // ================================ TABLE SETUP ================================ //

  /**
   * Get table column configuration
   * @param {string|Object} col - Column definition
   * @param {Object} chart_config - Chart configuration
   * @returns {Object} Column configuration object
   */
  function getTableColumn(col, chart_config) {
    const is_table = chart_config?.type === 'table';

    // Handle the case when `col` is a string
    if (typeof col === 'string') {
      const width = is_table ? chart_config?.columns_map?.[col]?.width ?? null : null;

      return {
        label: col,
        data: col,
        type: 'text',
        readOnly: true,
        field: col,
        ...(width && { width }),
      };
    }

    // When `col` is an object
    const key = col.key || col.label;
    const field = col.field || key;
    const width = is_table ? chart_config?.columns_map?.[key]?.width ?? null : null;

    return {
      label: col.label,
      data: key,
      type: 'text',
      readOnly: true,
      field,
      ...(width && { width }),
    };
  }

  /**
   * Setup pivot table configuration
   * @param {Object} params - Setup parameters
   * @returns {Object} Pivot table configuration
   */
  async function setupPivotTable({
    state,
    chart_config,
    bi_store,
    isEditing,
    reinitialize = true,
  }) {
    if (isEditing && reinitialize) {
      const current_columns = state.columns;

      // Previous config columns
      const prev_rows = chart_config.pivot_rows || [];
      const prev_cols = chart_config.pivot_columns || [];
      const prev_vals = chart_config.pivot_values || [];

      const alias_map = bi_store.alias_to_column_mapping();

      const previous_columns = [...prev_rows, ...prev_cols, ...prev_vals];

      const current_set = new Set(current_columns);
      const previous_set = new Set(previous_columns);

      const has_column_changes = (
        current_columns.length !== previous_columns.length
        || current_columns.some(col => !previous_set.has(col))
        || previous_columns.some(col => !current_set.has(col))
      );

      if (has_column_changes) {
        const updated_rows = [];
        const updated_cols = [];
        const updated_vals = [];

        // Re-add existing ones (that still exist)
        for (const col of prev_rows) {
          if (current_set.has(col))
            updated_rows.push(col);
        }
        for (const col of prev_cols) {
          if (current_set.has(col))
            updated_cols.push(col);
        }
        for (const col of prev_vals) {
          if (current_set.has(col))
            updated_vals.push(col);
        }

        // Identify new columns
        const new_columns = current_columns.filter(col => !previous_set.has(col));

        // Classify and assign new columns
        const new_value_keys = [];
        const new_non_value_keys = [];

        for (const col of new_columns) {
          const details = alias_map[col];
          if (details?.is_aggregation || ['integer', 'numeric'].includes(details?.type)) {
            new_value_keys.push(col);
          }
          else {
            new_non_value_keys.push(col);
          }
        }

        // Assign new value keys
        updated_vals.push(...new_value_keys);

        // Assign new non-value keys by balancing between rows and columns
        for (const col of new_non_value_keys) {
          if (updated_rows.length <= updated_cols.length) {
            updated_rows.push(col);
          }
          else {
            updated_cols.push(col);
          }
        }

        bi_store.widget_builder_config.chart.pivot_rows = updated_rows;
        bi_store.widget_builder_config.chart.pivot_columns = updated_cols;
        bi_store.widget_builder_config.chart.pivot_values = updated_vals;
      }
    }

    const { pivot_rows = [], pivot_columns = [], pivot_values = [] } = chart_config;

    const nested_headers = await generateNestedTableHeaders(
      state.data,
      pivot_columns,
      pivot_values,
      pivot_rows,
      '|',
      chart_config,
    );

    state.table_columns = nested_headers.slice(-1)[0].map(col => getTableColumn(col, chart_config));
    state.nested_headers = nested_headers;

    const nested_data = await generateHandsontableData(
      state.data,
      pivot_columns,
      pivot_values,
      pivot_rows,
      '|',
      chart_config,
    );

    if (pivot_rows?.length)
      state.nested_rows = true;

    state.table_data = nested_data;

    return {
      table_columns: state.table_columns,
      nested_headers: state.nested_headers,
      table_data: state.table_data,
      nested_rows: state.nested_rows,
    };
  }

  /**
   * Setup regular table configuration
   * @param {Object} params - Setup parameters
   * @returns {Object} Table configuration
   */
  async function setupTable({
    state,
    chart_config,
    bi_store,
    isBuilder,
    isEditing,
    reinitialize = true,
  }) {
    if (!isBuilder && isEditing && reinitialize) {
      const columns_map = state.columns.reduce((acc, col_key, index) => {
        const col_data = bi_store.widget_builder_config.chart.columns_map?.[col_key] || {};
        acc[col_key] = {
          key: col_key,
          width: get(col_data, 'width', null),
          visible: get(col_data, 'visible', true),
          order_index: get(col_data, 'order_index', index),
        };
        return acc;
      }, {});
      bi_store.widget_builder_config.chart.columns_map = cloneDeep(columns_map);
    }

    if (isBuilder) {
      state.table_columns = state.columns.map(col => getTableColumn(col, chart_config));
    }
    else {
      state.table_columns = orderBy(
        state.columns.filter(col =>
          get(chart_config?.columns_map || {}, `${col}.visible`, true),
        ),
        col => get(chart_config?.columns_map || {}, `${col}.order_index`, 0),
      ).map(col => getTableColumn(col, chart_config));
    }

    state.table_data = state.data;
    if (isBuilder) {
      state.column_config = state.columns.reduce((acc, col) => {
        const column_details = bi_store.alias_to_column_mapping()[col];
        if (column_details?.is_aggregation) {
          acc[col] = { backgroundColor: '#FFFAEB' };
        }
        return acc;
      }, {});
    }

    return {
      table_columns: state.table_columns,
      table_data: state.table_data,
      column_config: state.column_config,
    };
  }

  // ================================ TABLE FORMATTING ================================ //

  /**
   * Parse a value to a safe number
   * @param {any} value - Value to parse
   * @returns {number} Parsed number or NaN
   */
  function parseSafeNumber(value) {
    if (value === null || value === undefined || value === '') {
      return Number.NaN;
    }
    const num = Number(value);
    return Number.isNaN(num) ? Number.NaN : num;
  }

  /**
   * Get min and max values for a specific field from table data
   * @param {Object} instance - Handsontable instance
   * @param {Object} rule - Formatting rule
   * @param {Array} table_columns - Table columns configuration
   * @returns {Object|null} Object with min and max values or null
   */
  function getMinMax(instance, rule, table_columns) {
    const columns = table_columns.map((col, index) => ({ index, field: col.field })).filter(col => col.field === rule.field);
    let values = [];
    for (const col of columns) {
      values = values.concat(instance.getDataAtCol(col.index).map(val => parseSafeNumber(val)).filter(val => !Number.isNaN(val)));
    }
    if (values.length) {
      return {
        min: Math.min(...values),
        max: Math.max(...values),
      };
    }
    return null;
  }

  /**
   * Get cell formatting based on conditional formatting rules
   * @param {Object} params - Formatting parameters
   * @returns {Object|null} CSS styles object or null
   */
  function getCellFormatting({ column, row_data, instance, value, chart_config, table_columns }) {
    // For total rows and columns styles
    if (row_data.__is_grand_total) {
      return {
        'background-color': '#475467',
        'font-weight': 600,
        'color': '#FFFFFF',
      };
    }
    if (row_data.__is_column_total || column?.data?.includes('__row_total_')) {
      return {
        'background-color': '#EAECF0',
        'font-weight': 600,
        'color': '#101828 !important',
      };
    }

    let rules = [];
    if (chart_config?.type) {
      rules = chart_config?.conditional_formatting || [];
    }
    const min_max_map = {};
    for (const rule of rules) {
      if (rule.formatting_style === 'single_color') {
        if (checkRuleMatch(rule, row_data, column, value)) {
          let color = rule.color;
          if (column.field !== rule.field) {
            color = getLighterColor(rule.color);
          }
          return {
            'background-color': color,
            'color': getContrastTextColor(color),
          };
        }
      }
      else {
        if (column.field !== rule.field)
          continue;
        let min = rule.start_range_value;
        let max = rule.end_range_value;
        if ((rule.start_range_at === 'min' || rule.end_range_at === 'max') && !min_max_map[column.field]) {
          min_max_map[rule.field] = getMinMax(instance, rule, table_columns);
        }
        if (rule.start_range_at === 'min') {
          min = min_max_map[rule.field]?.min;
        }
        if (rule.end_range_at === 'max') {
          max = min_max_map[rule.field]?.max;
        }
        const cell_value = parseSafeNumber(value);
        if (Number.isNaN(cell_value) || (cell_value < min || cell_value > max))
          continue;
        const color = getColorForValue(cell_value, min, max, rule.color, rule.color_range);
        return {
          'background-color': color,
          'color': getContrastTextColor(color),
        };
      }
    }
    return null;
  }

  // ================================ TABLE INTERACTIONS ================================ //

  /**
   * Handle column resize events
   * @param {Object} col_widths_map - Map of column keys to widths
   * @param {Object} params - Parameters object
   */
  function handleColumnResize(col_widths_map, { isEditing, chart_config, bi_store }) {
    if (!isEditing)
      return;
    if (chart_config.type === 'table') {
      Object.entries(col_widths_map).forEach(([col_key, width]) => {
        if (bi_store.widget_builder_config.chart.columns_map?.[col_key]) {
        // Update width in the columns map
          bi_store.widget_builder_config.chart.columns_map[col_key] = {
            ...bi_store.widget_builder_config.chart.columns_map[col_key],
            width,
          };
        }
      });
    }
  }

  /**
   * Auto-fit columns to content
   * @param {Object} hot - Handsontable instance
   * @param {Object} params - Parameters object
   */
  function autoFitColumns(hot, { chart_config, table_columns }) {
    nextTick(() => {
      const plugin = hot.getPlugin('autoColumnSize');
      plugin.recalculateAllColumnsWidth();
      const widths = [];
      for (let col = 0; col < hot.countCols(); col++) {
        if (chart_config?.type === 'table') {
          const column_key = table_columns[col].data;
          widths.push(chart_config?.columns_map?.[column_key]?.width || plugin.getColumnWidth(col));
        }
        else {
          widths.push(plugin.getColumnWidth(col));
        }
      }

      if (widths.length) {
        setTimeout(() => {
          hot.updateSettings({ colWidths: widths });
          hot.render();
        }, 100);
      }
    });
  }

  // ================================ PUBLIC API ================================ //

  return {
    // Table Setup
    getTableColumn,
    setupPivotTable,
    setupTable,

    // Table Formatting
    getCellFormatting,
    parseSafeNumber,
    getMinMax,

    // Table Interactions
    handleColumnResize,
    autoFitColumns,
  };
}
