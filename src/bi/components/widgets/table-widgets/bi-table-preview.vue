<script setup>
import { computed, onMounted, reactive, watch } from 'vue';
import B<PERSON><PERSON>andsontable from '~/bi/components/widgets/table-widgets/bi-handsontable.vue';
import { useTableFormatting } from '~/bi/composables/helpers/bi-table-formatting.composable.js';
import { useTableInteraction } from '~/bi/composables/helpers/bi-table-interaction.composable.js';
import { useTableSetup } from '~/bi/composables/helpers/bi-table-setup.composable.js';
import { useBiStore } from '~/bi/store/bi.store';

const props = defineProps({
  id: {
    type: String,
    default: () => crypto.randomUUID(),
  },
  data: {
    type: Array,
    default: null,
  },
  tableMetadata: {
    type: Object,
    default: () => ({}),
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  isBuilder: {
    type: Boolean,
    default: true,
  },
  isEditing: {
    type: <PERSON>olean,
    default: true,
  },
  height: {
    type: Number,
    default: 0,
  },
});

const bi_store = useBiStore();
const { setupPivotTable, setupTable } = useTableSetup();
const { getCellFormatting } = useTableFormatting();
const { handleColumnResize, autoFitColumns } = useTableInteraction();

const state = reactive({
  loading: false,
  error: null,
  data: [],
  columns: [],

  table_columns: [],
  table_data: [],
  nested_headers: [],
  nested_rows: false,
  column_config: {},

  table_instance: null,

  changes_detected: false,
  loading_changes: false,
  force_update: 1, // Will be updated in the future to update settings
});

const chart_config = computed(() => {
  const chart_details = props.config?.chart || {};
  const {
    type,

    columns_map = {},
    show_row_headers = false,

    pivot_rows = [],
    pivot_columns = [],
    pivot_values = [],
    show_row_totals = false,
    show_column_totals = false,
    show_grand_totals = false,

    conditional_formatting = [],
  } = chart_details;

  if (type === 'table') {
    return { type, columns_map, conditional_formatting, show_row_headers };
  }
  else if (type === 'pivot_table') {
    return { type, pivot_rows, pivot_columns, pivot_values, show_row_totals, show_grand_totals, show_column_totals, conditional_formatting };
  }
  return chart_details;
});
const enable_row_headers = computed(() => {
  if (chart_config.value?.type === 'table')
    return chart_config.value?.show_row_headers || false;
  return false;
});
const table_height = computed(() => {
  if (props.height)
    return props.height;
  // Header and Footer is about 85 each total 190
  // Additional table padding 40
  const available_height = window.innerHeight - 220;
  return available_height;
});
// -------------------------------- Methods --------------------------------- //

async function initializeTable(reinitialize = true) {
  state.data = props.data;
  state.columns = (props.tableMetadata?.columns || [])?.map(col => col.name);

  if (chart_config.value?.type === 'pivot_table') {
    setupPivotTable({
      state,
      chart_config: chart_config.value,
      bi_store,
      isEditing: props.isEditing,
      reinitialize,
    });
  }
  else {
    setupTable({
      state,
      chart_config: chart_config.value,
      bi_store,
      isBuilder: props.isBuilder,
      isEditing: props.isEditing,
      reinitialize,
    });
  }
  bi_store.is_table_dirty = false;
}

watch(() => (bi_store.is_table_dirty), () => {
  if (!props.isEditing)
    return;
  // state.changes_detected = true;
  initializeTable(false);
  state.force_update++;
});
// -------------------------------- Lifecycle ------------------------------- //
onMounted(() => {
  initializeTable();
});
</script>

<template>
  <div class="h-[100%] w-full">
    <div
      v-if="state.changes_detected"
      class="flex items-center justify-center w-full h-full"
    >
      <!-- Popup card -->
      <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6 flex flex-col items-center min-w-[600px]">
        <HawkFeaturedIcon theme="light-circle-outline" size="md" color="primary" class="mb-4">
          <IconHawkInfoCircle />
        </HawkFeaturedIcon>

        <div class="text-sm font-semibold mb-1">
          {{ $t('Changes detected') }}
        </div>

        <div class="text-sm text-gray-600 mb-6">
          {{ $t('New changes is been detected. You can reload to view new changes.') }}
        </div>

        <HawkButton :loading="state.loading_changes" @click="fetchTableData(bi_store.is_table_dirty)">
          <IconHawkRefreshCwTwo />
          <span class="text-sm font-medium">
            {{ $t('Load Preview') }}
          </span>
        </HawkButton>
      </div>
    </div>
    <div v-else-if="!state.table_data?.length">
      <div class="flex items-center justify-center w-full h-full">
        <HawkIllustrations type="no-data" for="bi-table" />
      </div>
    </div>
    <BiHandsontable
      v-else
      :key="state.force_update"
      :bi-table-id="id"
      :height="table_height"
      :data="state.table_data"
      :columns="state.table_columns"
      :column-config="state.column_config"
      :nested-headers="state.nested_headers"
      :nested-rows="state.nested_rows"
      :show-skeleton-loader="state.loading"
      :get-cell-formatting="(params) => getCellFormatting({ ...params, chart_config, table_columns: state.table_columns })"
      :row-headers="enable_row_headers"
      class="h-full border"
      :enable-column-sorting="chart_config.type !== 'pivot_table'"
      @table-instance="state.table_instance = $event"
      @after-load-data="(hot) => autoFitColumns(hot, { chart_config, table_columns: state.table_columns })"
      @column-resize="(col_widths_map) => handleColumnResize(col_widths_map, { isEditing: props.isEditing, chart_config, bi_store })"
    />
  </div>
</template>

<style scoped lang="scss">
// Add any custom styles here
</style>
