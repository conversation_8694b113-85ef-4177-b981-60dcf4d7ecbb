import { cloneDeep, get, orderBy } from 'lodash-es';
import { usePivotTableGenerator } from './bi-pivot-table-generator.composable.js';
import { usePivotDataProcessor } from './bi-pivot-data-processor.composable.js';

/**
 * Helper composable for table setup and configuration
 * Contains logic for setting up both regular tables and pivot tables
 */
export function useTableSetup() {
  const { generateNestedTableHeaders } = usePivotTableGenerator();
  const { generateHandsontableData } = usePivotDataProcessor();

  /**
   * Get table column configuration
   * @param {string|Object} col - Column definition
   * @param {Object} chart_config - Chart configuration
   * @returns {Object} Column configuration object
   */
  function getTableColumn(col, chart_config) {
    const is_table = chart_config?.type === 'table';

    // Handle the case when `col` is a string
    if (typeof col === 'string') {
      const width = is_table ? chart_config?.columns_map?.[col]?.width ?? null : null;

      return {
        label: col,
        data: col,
        type: 'text',
        readOnly: true,
        field: col,
        ...(width && { width }),
      };
    }

    // When `col` is an object
    const key = col.key || col.label;
    const field = col.field || key;
    const width = is_table ? chart_config?.columns_map?.[key]?.width ?? null : null;

    return {
      label: col.label,
      data: key,
      type: 'text',
      readOnly: true,
      field,
      ...(width && { width }),
    };
  }

  /**
   * Setup pivot table configuration
   * @param {Object} params - Setup parameters
   * @returns {Object} Pivot table configuration
   */
  async function setupPivotTable({
    state,
    chart_config,
    bi_store,
    isEditing,
    reinitialize = true,
  }) {
    if (isEditing && reinitialize) {
      const current_columns = state.columns;

      // Previous config columns
      const prev_rows = chart_config.pivot_rows || [];
      const prev_cols = chart_config.pivot_columns || [];
      const prev_vals = chart_config.pivot_values || [];

      const alias_map = bi_store.alias_to_column_mapping();

      const previous_columns = [...prev_rows, ...prev_cols, ...prev_vals];

      const current_set = new Set(current_columns);
      const previous_set = new Set(previous_columns);

      const has_column_changes = (
        current_columns.length !== previous_columns.length
        || current_columns.some(col => !previous_set.has(col))
        || previous_columns.some(col => !current_set.has(col))
      );

      if (has_column_changes) {
        const updated_rows = [];
        const updated_cols = [];
        const updated_vals = [];

        // Re-add existing ones (that still exist)
        for (const col of prev_rows) {
          if (current_set.has(col))
            updated_rows.push(col);
        }
        for (const col of prev_cols) {
          if (current_set.has(col))
            updated_cols.push(col);
        }
        for (const col of prev_vals) {
          if (current_set.has(col))
            updated_vals.push(col);
        }

        // Identify new columns
        const new_columns = current_columns.filter(col => !previous_set.has(col));

        // Classify and assign new columns
        const new_value_keys = [];
        const new_non_value_keys = [];

        for (const col of new_columns) {
          const details = alias_map[col];
          if (details?.is_aggregation || ['integer', 'numeric'].includes(details?.type)) {
            new_value_keys.push(col);
          }
          else {
            new_non_value_keys.push(col);
          }
        }

        // Assign new value keys
        updated_vals.push(...new_value_keys);

        // Assign new non-value keys by balancing between rows and columns
        for (const col of new_non_value_keys) {
          if (updated_rows.length <= updated_cols.length) {
            updated_rows.push(col);
          }
          else {
            updated_cols.push(col);
          }
        }

        bi_store.widget_builder_config.chart.pivot_rows = updated_rows;
        bi_store.widget_builder_config.chart.pivot_columns = updated_cols;
        bi_store.widget_builder_config.chart.pivot_values = updated_vals;
      }
    }

    const { pivot_rows = [], pivot_columns = [], pivot_values = [] } = chart_config;

    const nested_headers = await generateNestedTableHeaders(
      state.data,
      pivot_columns,
      pivot_values,
      pivot_rows,
      '|',
      chart_config,
    );

    state.table_columns = nested_headers.slice(-1)[0].map(col => getTableColumn(col, chart_config));
    state.nested_headers = nested_headers;

    const nested_data = await generateHandsontableData(
      state.data,
      pivot_columns,
      pivot_values,
      pivot_rows,
      '|',
      chart_config,
    );

    if (pivot_rows?.length)
      state.nested_rows = true;

    state.table_data = nested_data;

    return {
      table_columns: state.table_columns,
      nested_headers: state.nested_headers,
      table_data: state.table_data,
      nested_rows: state.nested_rows,
    };
  }

  /**
   * Setup regular table configuration
   * @param {Object} params - Setup parameters
   * @returns {Object} Table configuration
   */
  async function setupTable({
    state,
    chart_config,
    bi_store,
    isBuilder,
    isEditing,
    reinitialize = true,
  }) {
    if (!isBuilder && isEditing && reinitialize) {
      const columns_map = state.columns.reduce((acc, col_key, index) => {
        const col_data = bi_store.widget_builder_config.chart.columns_map?.[col_key] || {};
        acc[col_key] = {
          key: col_key,
          width: get(col_data, 'width', null),
          visible: get(col_data, 'visible', true),
          order_index: get(col_data, 'order_index', index),
        };
        return acc;
      }, {});
      bi_store.widget_builder_config.chart.columns_map = cloneDeep(columns_map);
    }

    if (isBuilder) {
      state.table_columns = state.columns.map(col => getTableColumn(col, chart_config));
    }
    else {
      state.table_columns = orderBy(
        state.columns.filter(col =>
          get(chart_config?.columns_map || {}, `${col}.visible`, true),
        ),
        col => get(chart_config?.columns_map || {}, `${col}.order_index`, 0),
      ).map(col => getTableColumn(col, chart_config));
    }

    state.table_data = state.data;
    if (isBuilder) {
      state.column_config = state.columns.reduce((acc, col) => {
        const column_details = bi_store.alias_to_column_mapping()[col];
        if (column_details?.is_aggregation) {
          acc[col] = { backgroundColor: '#FFFAEB' };
        }
        return acc;
      }, {});
    }

    return {
      table_columns: state.table_columns,
      table_data: state.table_data,
      column_config: state.column_config,
    };
  }

  return {
    getTableColumn,
    setupPivotTable,
    setupTable,
  };
}
