/**
 * Helper composable for generating pivot table headers and data
 * Contains complex logic for nested headers and data aggregation
 */
export function usePivotTableGenerator() {
  /**
   * Generate nested table headers for pivot tables
   * @param {Array} data - Raw data array
   * @param {Array} columns - Column pivot fields
   * @param {Array} values - Value fields to aggregate
   * @param {Array} group_by_keys - Row grouping fields
   * @param {string} delimiter - Delimiter for joining keys
   * @param {Object} chart_config - Chart configuration
   * @returns {Array} Nested headers array
   */
  async function generateNestedTableHeaders(data, columns, values, group_by_keys, delimiter = '|', chart_config) {
    const nested_headers = [];

    // Handle case: no column pivoting
    if (columns.length === 0) {
      const flat_header_row = [];

      // Add row headers
      for (const rh of group_by_keys) {
        flat_header_row.push({ label: rh, key: rh, field: rh });
      }

      // Add values directly as columns
      for (const val of values) {
        flat_header_row.push({ label: val, key: val, field: val });
      }

      if (chart_config?.show_row_totals) {
        for (const val of values) {
          flat_header_row.push({ label: `Total ${val}`, key: `__row_total_${val}` });
        }
      }

      nested_headers.push(flat_header_row);
      return nested_headers;
    }

    // Build tree structure for column pivoting
    function buildTree(data, level = 0) {
      if (level >= columns.length)
        return [];

      const groups = {};
      for (const row of data) {
        const key = row[columns[level]];
        if (!groups[key])
          groups[key] = [];
        groups[key].push(row);
      }

      const nodes = [];
      for (const key in groups) {
        const children = buildTree(groups[key], level + 1);
        nodes.push({ label: key, children });
      }
      return nodes;
    }

    const tree = buildTree(data);

    function countLeaves(node) {
      if (!node.children || node.children.length === 0)
        return 1;
      return node.children.reduce((sum, child) => sum + countLeaves(child), 0);
    }

    // Generate nested headers
    function fillHeaders(nodes, level = 0) {
      if (!nested_headers[level]) {
        nested_headers[level] = [];
        for (let i = 0; i < group_by_keys.length; i++) {
          nested_headers[level].push({ label: '', colspan: 1 });
        }
      }

      for (const node of nodes) {
        const leaf_count = countLeaves(node);
        nested_headers[level].push({
          label: node.label,
          colspan: leaf_count * values.length,
        });

        if (node.children.length > 0) {
          fillHeaders(node.children, level + 1);
        }
      }
    }

    fillHeaders(tree);

    const final_row = [];
    for (const rh of group_by_keys) {
      final_row.push({ label: rh, key: rh, field: rh });
    }

    function pushLeafLabels(nodes, path = []) {
      for (const node of nodes) {
        const currentPath = [...path, node.label];
        if (node.children.length > 0) {
          pushLeafLabels(node.children, currentPath);
        }
        else {
          for (const val of values) {
            final_row.push({
              label: val,
              key: currentPath.concat(val).join(delimiter),
              field: val,
            });
          }
        }
      }
    }

    pushLeafLabels(tree);
    if (chart_config?.show_row_totals) {
      for (let i = 1; i <= nested_headers.length; i++) {
        nested_headers[i - 1].push({ label: i === nested_headers.length ? 'Row Total' : '', colspan: values.length });
      }
      for (const val of values) {
        final_row.push({
          label: `Total ${val}`,
          key: `__row_total_${val}`,
        });
      }
    }
    nested_headers.push(final_row);
    return nested_headers;
  }

  return {
    generateNestedTableHeaders,
  };
}
