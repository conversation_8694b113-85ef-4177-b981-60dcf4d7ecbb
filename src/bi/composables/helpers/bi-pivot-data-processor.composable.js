/**
 * Helper composable for processing pivot table data
 * Contains complex logic for data aggregation and grouping
 */
export function usePivotDataProcessor() {
  /**
   * Generate Handsontable-compatible data from pivot configuration
   * @param {Array} data - Raw data array
   * @param {Array} columns - Column pivot fields
   * @param {Array} values - Value fields to aggregate
   * @param {Array} group_by_keys - Row grouping fields
   * @param {string} delimiter - Delimiter for joining keys
   * @param {Object} chart_config - Chart configuration
   * @returns {Array} Processed data array
   */
  function generateHandsontableData(data, columns, values, group_by_keys, delimiter = '|', chart_config) {
    // Helper: group rows recursively
    function normalizeKey(val) {
      // Consistent key format for grouping — handles undefined/null gracefully
      return val === undefined || val === null ? '' : String(val);
    }

    function getRowKeys(row) {
      const column_key = columns.length
        ? columns.map(col => normalizeKey(row[col])).join(delimiter)
        : '';
      const row_keys = [];
      for (const val of values) {
        if (column_key.length) {
          row_keys.push({ data_key: `${column_key}${delimiter}${val}`, value_key: val });
        }
        else {
          row_keys.push({ data_key: val, value_key: val });
        }
      }
      return row_keys;
    }

    function formatRow(row, node = {}) {
      const row_keys = getRowKeys(row);
      for (const key of row_keys) {
        node[key.data_key] = row[key.value_key] ?? null;
        node[key.value_key] = node[key.data_key];
      }
      node.__actual_row = row;

      if (chart_config?.show_row_totals) {
        for (const val of values) {
          node[`__row_total_${val}`] = row[val] ?? null;
        }
      }

      return node;
    }

    function groupByHeaders(rows, headers) {
      if (headers.length === 0) {
        return rows.map(row => formatRow(row));
      }

      const [current, ...rest] = headers;
      const groups = {};

      rows.forEach((row) => {
        const key = normalizeKey(row[current]);
        if (!groups[key]) {
          groups[key] = [];
        }
        groups[key].push(row);
      });

      return Object.entries(groups).map(([key, grouped_rows]) => {
        const node = { [current]: key };

        if (rest.length > 0) {
          node.__is_group = true;
          node.__is_group_column = current;
          node.__children = groupByHeaders(grouped_rows, rest);

          if (chart_config?.show_column_totals || chart_config?.show_row_totals) {
            for (const row of grouped_rows) {
              if (chart_config?.show_column_totals) {
                const row_keys = getRowKeys(row);
                for (const key of row_keys) {
                  const existing = Number(node[key.data_key] ?? 0);
                  const current = Number(row[key.value_key] ?? 0);
                  const sum = existing + (Number.isNaN(current) ? 0 : current);
                  node[key.data_key] = String(sum);
                  node.__is_column_total = true;
                }
              }

              if (chart_config?.show_row_totals) {
                for (const val of values) {
                  const existing = Number(node[`__row_total_${val}`] ?? 0);
                  const current = Number(row[val] ?? 0);
                  const sum = existing + (Number.isNaN(current) ? 0 : current);
                  node[`__row_total_${val}`] = String(sum);
                }
              }
            }
          }
        }
        else {
          node.__is_leaf_group = true;

          for (const row of grouped_rows) {
            const row_keys = getRowKeys(row);

            // aggregate value columns
            for (const key of row_keys) {
              const existing = Number(node[key.data_key] ?? 0);
              const current = Number(row[key.value_key] ?? 0);
              const sum = existing + (Number.isNaN(current) ? 0 : current);
              node[key.data_key] = String(sum);
              node[key.value_key] = String(sum);
            }

            // aggregate row totals if enabled
            if (chart_config?.show_row_totals) {
              for (const val of values) {
                const existing = Number(node[`__row_total_${val}`] ?? 0);
                const current = Number(row[val] ?? 0);
                const sum = existing + (Number.isNaN(current) ? 0 : current);
                node[`__row_total_${val}`] = String(sum);
              }
            }
          }
        }

        return node;
      });
    }

    const nested_data = groupByHeaders(data, group_by_keys);

    // --- Grand Total Column ---
    if (chart_config?.show_grand_totals) {
      const grand_total = { [group_by_keys[0] || 'Total']: 'Grand Totals', __is_grand_total: true };

      for (const row of data) {
      // aggregate per-column values
        const row_keys = getRowKeys(row);
        for (const key of row_keys) {
          const existing = Number(grand_total[key.data_key] ?? 0);
          const current = Number(row[key.value_key] ?? 0);
          grand_total[key.data_key] = existing + (Number.isNaN(current) ? 0 : current);
          grand_total[key.data_key] = String(grand_total[key.data_key]);
        }

        // aggregate per-row totals
        for (const val of values) {
          const existing = Number(grand_total[`__row_total_${val}`] ?? 0);
          const current = Number(row[val] ?? 0);
          grand_total[`__row_total_${val}`] = existing + (Number.isNaN(current) ? 0 : current);
          grand_total[`__row_total_${val}`] = String(grand_total[`__row_total_${val}`]);
        }
      }
      nested_data.push(grand_total);
    }

    return nested_data;
  }

  return {
    generateHandsontableData,
  };
}
