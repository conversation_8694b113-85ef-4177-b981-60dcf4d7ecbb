import { nextTick } from 'vue';

/**
 * Helper composable for table interactions
 * Contains logic for handling user interactions with the table
 */
export function useTableInteraction() {
  /**
   * Handle column resize events
   * @param {Object} col_widths_map - Map of column keys to widths
   * @param {Object} params - Parameters object
   */
  function handleColumnResize(col_widths_map, { isEditing, chart_config, bi_store }) {
    if (!isEditing)
      return;
    if (chart_config.type === 'table') {
      Object.entries(col_widths_map).forEach(([col_key, width]) => {
        if (bi_store.widget_builder_config.chart.columns_map?.[col_key]) {
        // Update width in the columns map
          bi_store.widget_builder_config.chart.columns_map[col_key] = {
            ...bi_store.widget_builder_config.chart.columns_map[col_key],
            width,
          };
        }
      });
    }
  }

  /**
   * Auto-fit columns to content
   * @param {Object} hot - Handsontable instance
   * @param {Object} params - Parameters object
   */
  function autoFitColumns(hot, { chart_config, table_columns }) {
    nextTick(() => {
      const plugin = hot.getPlugin('autoColumnSize');
      plugin.recalculateAllColumnsWidth();
      const widths = [];
      for (let col = 0; col < hot.countCols(); col++) {
        if (chart_config?.type === 'table') {
          const column_key = table_columns[col].data;
          widths.push(chart_config?.columns_map?.[column_key]?.width || plugin.getColumnWidth(col));
        }
        else {
          widths.push(plugin.getColumnWidth(col));
        }
      }

      if (widths.length) {
        setTimeout(() => {
          hot.updateSettings({ colWidths: widths });
          hot.render();
        }, 100);
      }
    });
  }

  return {
    handleColumnResize,
    autoFitColumns,
  };
}
